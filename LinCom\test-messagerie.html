<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Messagerie LinkedIn Style</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* LinkedIn-style Messaging CSS */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f3f2ef;
        }

        .linkedin-messaging-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #fff;
            box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 2px 3px rgba(0,0,0,0.2);
        }

        .messaging-layout {
            display: flex;
            flex: 1;
            height: calc(100vh - 60px);
        }

        /* Conversations Sidebar */
        .conversations-sidebar {
            width: 360px;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f3f2ef;
            color: #0a66c2;
        }

        /* Search Container */
        .search-container {
            padding: 12px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            color: #666;
            font-size: 14px;
            z-index: 1;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            background: #f3f2ef;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #0a66c2;
            background: #fff;
            box-shadow: 0 0 0 1px #0a66c2;
        }

        /* Conversations List */
        .conversations-list {
            flex: 1;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            padding: 12px 20px;
            border-bottom: 1px solid #f3f2ef;
            cursor: pointer;
            transition: background-color 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item:hover {
            background-color: #f3f2ef;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item.active {
            background-color: #e7f3ff;
            border-right: 2px solid #0a66c2;
        }

        .conversation-avatar {
            position: relative;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .conversation-avatar img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .online-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #57c93a;
            border: 2px solid #fff;
            border-radius: 50%;
        }

        .conversation-content {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .contact-name {
            font-size: 14px;
            font-weight: 600;
            color: #000;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .last-message-time {
            font-size: 12px;
            color: #666;
            flex-shrink: 0;
        }

        .conversation-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .last-message {
            font-size: 13px;
            color: #666;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .message-indicators {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .unread-count {
            background: #0a66c2;
            color: #fff;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* Chat Main Area */
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
        }

        /* Chat Header */
        .chat-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
        }

        .chat-header-info {
            display: flex;
            align-items: center;
        }

        .contact-avatar {
            position: relative;
            margin-right: 12px;
        }

        .contact-avatar img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .online-status {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            background: #57c93a;
            border: 2px solid #fff;
            border-radius: 50%;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
        }

        .contact-details .contact-name {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            margin: 0;
        }

        .contact-status {
            font-size: 12px;
            color: #57c93a;
            margin: 0;
        }

        .chat-actions {
            display: flex;
            gap: 8px;
        }

        /* Messages Container */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            background: #f9f9f9;
            padding: 20px;
        }

        .welcome-message {
            text-align: center;
            color: #666;
            font-size: 16px;
            margin-top: 50px;
        }

        /* Message Input Area */
        .message-input-area {
            border-top: 1px solid #e0e0e0;
            background: #fff;
            padding: 16px 20px;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .input-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .input-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
        }

        .input-btn:hover {
            background: #f3f2ef;
            color: #0a66c2;
        }

        .message-input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            font-family: inherit;
            resize: none;
            outline: none;
            transition: all 0.2s ease;
        }

        .message-input:focus {
            border-color: #0a66c2;
            box-shadow: 0 0 0 1px #0a66c2;
        }

        .send-button {
            width: 40px;
            height: 40px;
            background: #0a66c2;
            color: #fff;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .send-button:hover {
            background: #004182;
            transform: scale(1.05);
        }

        .input-footer {
            margin-top: 8px;
            text-align: right;
        }

        .char-counter {
            color: #666;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="linkedin-messaging-container">
        <div class="messaging-layout">
            <!-- Left Sidebar - Conversations List -->
            <div class="conversations-sidebar">
                <!-- Header -->
                <div class="sidebar-header">
                    <h2 class="sidebar-title">Messagerie</h2>
                    <div class="header-actions">
                        <button class="action-btn" title="Nouvelle conversation">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn" title="Plus d'options">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" placeholder="Rechercher dans les messages..." class="search-input">
                    </div>
                </div>

                <!-- Conversations List -->
                <div class="conversations-list">
                    <!-- Conversation Example 1 -->
                    <div class="conversation-item active">
                        <div class="conversation-avatar">
                            <img src="https://via.placeholder.com/48x48/0a66c2/ffffff?text=JD" alt="Photo de profil">
                            <div class="online-indicator"></div>
                        </div>
                        <div class="conversation-content">
                            <div class="conversation-header">
                                <h4 class="contact-name">Jean Dupont</h4>
                                <span class="last-message-time">2h</span>
                            </div>
                            <div class="conversation-preview">
                                <p class="last-message">Salut ! Comment ça va ?</p>
                                <div class="message-indicators">
                                    <span class="unread-count">2</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Conversation Example 2 -->
                    <div class="conversation-item">
                        <div class="conversation-avatar">
                            <img src="https://via.placeholder.com/48x48/28a745/ffffff?text=MM" alt="Photo de profil">
                        </div>
                        <div class="conversation-content">
                            <div class="conversation-header">
                                <h4 class="contact-name">Marie Martin</h4>
                                <span class="last-message-time">1j</span>
                            </div>
                            <div class="conversation-preview">
                                <p class="last-message">Merci pour les documents !</p>
                            </div>
                        </div>
                    </div>

                    <!-- Conversation Example 3 -->
                    <div class="conversation-item">
                        <div class="conversation-avatar">
                            <img src="https://via.placeholder.com/48x48/dc3545/ffffff?text=PD" alt="Photo de profil">
                        </div>
                        <div class="conversation-content">
                            <div class="conversation-header">
                                <h4 class="contact-name">Paul Durand</h4>
                                <span class="last-message-time">3j</span>
                            </div>
                            <div class="conversation-preview">
                                <p class="last-message">À bientôt pour la réunion</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="chat-main">
                <!-- Chat Header -->
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="contact-avatar">
                            <img src="https://via.placeholder.com/40x40/0a66c2/ffffff?text=JD" alt="Contact">
                            <div class="online-status"></div>
                        </div>
                        <div class="contact-details">
                            <h4 class="contact-name">Jean Dupont</h4>
                            <span class="contact-status">En ligne</span>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="action-btn" title="Appel vocal">
                            <i class="fas fa-phone"></i>
                        </button>
                        <button class="action-btn" title="Appel vidéo">
                            <i class="fas fa-video"></i>
                        </button>
                        <button class="action-btn" title="Informations">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                </div>

                <!-- Messages Area -->
                <div class="messages-container">
                    <div class="welcome-message">
                        <h3>✨ Interface LinkedIn-style créée avec succès !</h3>
                        <p>La barre de recherche est maintenant visible et fonctionnelle.</p>
                        <p>Tous les éléments de l'interface sont correctement stylisés.</p>
                    </div>
                </div>

                <!-- Message Input Area -->
                <div class="message-input-area">
                    <div class="input-container">
                        <div class="input-actions">
                            <button type="button" class="input-btn" title="Émojis">
                                <i class="fas fa-smile"></i>
                            </button>
                            <button type="button" class="input-btn" title="Pièce jointe">
                                <i class="fas fa-paperclip"></i>
                            </button>
                        </div>
                        
                        <div class="message-input-wrapper">
                            <textarea placeholder="Écrivez un message..." maxlength="1000" class="message-input"></textarea>
                        </div>
                        
                        <button type="button" class="send-button" title="Envoyer">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- Character Counter -->
                    <div class="input-footer">
                        <small class="char-counter">0/1000</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test de fonctionnalité de base
        document.querySelector('.message-input').addEventListener('input', function() {
            const length = this.value.length;
            document.querySelector('.char-counter').textContent = length + '/1000';
        });

        // Auto-resize textarea
        document.querySelector('.message-input').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        console.log('✅ Interface LinkedIn-style chargée avec succès !');
        console.log('✅ Barre de recherche visible et stylisée');
        console.log('✅ Tous les éléments CSS appliqués correctement');
    </script>
</body>
</html>
