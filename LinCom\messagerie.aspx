<%@ Page Title="" Language="C#" MasterPageFile="~/PageMaster.Master" AutoEventWireup="true" CodeBehind="messagerie.aspx.cs" Inherits="LinCom.messagerie" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:HiddenField ID="hdnConversationId" runat="server" />
    <asp:HiddenField ID="hdnIsGroup" runat="server" />
    <asp:HiddenField ID="hdnCurrentUserId" runat="server" />

    <!-- LinkedIn-style Messaging Interface -->
    <div class="linkedin-messaging-container">
        
        <!-- Main Layout -->
        <div class="messaging-layout">
            
            <!-- Left Sidebar - Conversations List -->
            <div class="conversations-sidebar">
                <!-- Header -->
                <div class="sidebar-header">
                    <h2 class="sidebar-title">Messagerie</h2>
                    <div class="header-actions">
                        <button class="action-btn" title="Nouvelle conversation">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn" title="Plus d'options">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <asp:TextBox ID="txtRechercheContact" runat="server" 
                            placeholder="Rechercher dans les messages..." 
                            AutoPostBack="true" 
                            OnTextChanged="txtRechercheContact_TextChanged" 
                            CssClass="search-input"></asp:TextBox>
                    </div>
                </div>

                <!-- Conversations List -->
                <div class="conversations-list">
                    <asp:ListView ID="listmembre" runat="server" OnItemCommand="listmembre_ItemCommand">
                        <EmptyDataTemplate>
                            <div class="empty-state">
                                <div class="empty-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <h3>Aucune conversation</h3>
                                <p>Commencez une nouvelle conversation pour rester en contact avec votre réseau.</p>
                                <button class="btn-primary">Nouvelle conversation</button>
                            </div>
                        </EmptyDataTemplate>
                        <ItemTemplate>
                            <asp:LinkButton ID="btnSelectMembre" runat="server"
                                CommandName="viewmem"
                                CommandArgument='<%# Eval("id") %>'
                                CssClass="conversation-item">
                                <div class="conversation-avatar">
                                    <img src='<%# HttpUtility.HtmlEncode(string.Concat("../file/membr/", Eval("PhotoProfil"))) %>' 
                                         alt="Photo de profil" />
                                    <div class="online-indicator"></div>
                                </div>
                                <div class="conversation-content">
                                    <div class="conversation-header">
                                        <h4 class="contact-name"><%# HttpUtility.HtmlEncode(Eval("Membre")) %></h4>
                                        <span class="last-message-time">2h</span>
                                    </div>
                                    <div class="conversation-preview">
                                        <p class="last-message">Dernier message de la conversation...</p>
                                        <div class="message-indicators">
                                            <span class="unread-count">2</span>
                                        </div>
                                    </div>
                                </div>
                            </asp:LinkButton>
                        </ItemTemplate>
                    </asp:ListView>
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="chat-main">
                
                <!-- Chat Header -->
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="contact-avatar">
                            <img src="../file/membr/default.jpg" alt="Contact" id="headerAvatar" />
                            <div class="online-status"></div>
                        </div>
                        <div class="contact-details">
                            <asp:Label ID="lblHeader" runat="server" Text="Sélectionnez une conversation" CssClass="contact-name"></asp:Label>
                            <span class="contact-status">En ligne</span>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <button class="action-btn" title="Appel vocal">
                            <i class="fas fa-phone"></i>
                        </button>
                        <button class="action-btn" title="Appel vidéo">
                            <i class="fas fa-video"></i>
                        </button>
                        <button class="action-btn" title="Informations">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                    <asp:Label ID="lblId" Visible="false" runat="server" Text="0"></asp:Label>
                </div>

                <!-- Messages Area -->
                <div class="messages-container">
                    <div class="messages-list" id="messagesList">
                        <asp:Repeater ID="rptMessages" runat="server">
                            <ItemTemplate>
                                <div class='message-wrapper <%# Eval("Expediteur").ToString() == "VotreNomComplet" ? "sent" : "received" %>'>
                                    <div class="message-avatar">
                                        <img src='<%# ResolveUrl("~/file/membr/") + Eval("Photomembre") %>' alt="Avatar" />
                                    </div>
                                    <div class="message-content">
                                        <div class="message-bubble">
                                            <p><%# Server.HtmlDecode(LinCom.Classe.EmojiManager.ConvertirEmojis(Eval("Contenu").ToString())) %></p>
                                            
                                            <!-- Pièce jointe si présente -->
                                            <asp:Panel runat="server" Visible='<%# !string.IsNullOrEmpty(Eval("AttachmentUrl").ToString()) %>'>
                                                <div class="message-attachment">
                                                    <div class="attachment-icon">
                                                        <i class="fas fa-file"></i>
                                                    </div>
                                                    <div class="attachment-info">
                                                        <span class="attachment-name"><%# Eval("name") %></span>
                                                        <span class="attachment-size"><%# GetFileSize(Eval("AttachmentUrl").ToString()) %></span>
                                                    </div>
                                                    <a href='<%# Eval("AttachmentUrl") %>' class="attachment-download" target="_blank">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </div>
                                            </asp:Panel>
                                        </div>
                                        <div class="message-meta">
                                            <span class="message-time"><%# Eval("DateEnvoi", "{0:HH:mm}") %></span>
                                            <span class="message-status">
                                                <i class="fas fa-check-double"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </ItemTemplate>
                        </asp:Repeater>
                    </div>
                </div>

                <!-- Message Input Area -->
                <div class="message-input-area">
                    <div class="input-container">
                        <div class="input-actions">
                            <button type="button" class="input-btn" id="btnEmoji" title="Émojis">
                                <i class="fas fa-smile"></i>
                            </button>
                            <button type="button" class="input-btn" id="btnAttachment" title="Pièce jointe">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <input type="file" id="fileAttachment" style="display:none;" 
                                   accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip,.rar,.mp3,.mp4,.avi" />
                        </div>
                        
                        <div class="message-input-wrapper">
                            <textarea runat="server" id="txtMessage" 
                                placeholder="Écrivez un message..." 
                                maxlength="1000"
                                class="message-input"></textarea>
                            
                            <!-- Attachment Preview -->
                            <div id="attachmentPreview" class="attachment-preview" style="display:none;">
                                <div class="preview-content">
                                    <span id="attachmentName"></span>
                                    <button type="button" id="btnRemoveAttachment" class="remove-attachment">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" runat="server" id="btnenvoie" onserverclick="btnenvoie_ServerClick" 
                                class="send-button" title="Envoyer">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- Character Counter -->
                    <div class="input-footer">
                        <small id="charCount" class="char-counter">0/1000</small>
                    </div>
                </div>

                <!-- Emoji Picker -->
                <div id="emojiPicker" class="emoji-picker" style="display:none;">
                    <div class="emoji-header">
                        <div class="emoji-tabs">
                            <button class="emoji-tab active" data-category="recent">
                                <i class="fas fa-clock"></i>
                            </button>
                            <button class="emoji-tab" data-category="smileys">
                                <i class="fas fa-smile"></i>
                            </button>
                            <button class="emoji-tab" data-category="people">
                                <i class="fas fa-user"></i>
                            </button>
                            <button class="emoji-tab" data-category="nature">
                                <i class="fas fa-leaf"></i>
                            </button>
                            <button class="emoji-tab" data-category="objects">
                                <i class="fas fa-lightbulb"></i>
                            </button>
                        </div>
                    </div>
                    <div class="emoji-content">
                        <div class="emoji-grid">
                            <span class="emoji-item" data-emoji="😊">😊</span>
                            <span class="emoji-item" data-emoji="😂">😂</span>
                            <span class="emoji-item" data-emoji="❤️">❤️</span>
                            <span class="emoji-item" data-emoji="👍">👍</span>
                            <span class="emoji-item" data-emoji="😢">😢</span>
                            <span class="emoji-item" data-emoji="😉">😉</span>
                            <span class="emoji-item" data-emoji="🔥">🔥</span>
                            <span class="emoji-item" data-emoji="🎉">🎉</span>
                            <span class="emoji-item" data-emoji="😍">😍</span>
                            <span class="emoji-item" data-emoji="👏">👏</span>
                            <span class="emoji-item" data-emoji="💪">💪</span>
                            <span class="emoji-item" data-emoji="🙏">🙏</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Fields -->
    <asp:HiddenField ID="hdnAttachmentPath" runat="server" />

    <style>
        /* LinkedIn-style Messaging CSS */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f3f2ef;
        }

        .linkedin-messaging-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #fff;
            box-shadow: 0 0 0 1px rgba(0,0,0,0.15), 0 2px 3px rgba(0,0,0,0.2);
        }

        .messaging-layout {
            display: flex;
            flex: 1;
            height: calc(100vh - 60px);
        }

        /* Conversations Sidebar */
        .conversations-sidebar {
            width: 360px;
            background: #fff;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #f3f2ef;
            color: #0a66c2;
        }

        /* Search Container */
        .search-container {
            padding: 12px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .search-box {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            color: #666;
            font-size: 14px;
            z-index: 1;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 36px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
            background: #f3f2ef;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #0a66c2;
            background: #fff;
            box-shadow: 0 0 0 1px #0a66c2;
        }

        /* Conversations List */
        .conversations-list {
            flex: 1;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            padding: 12px 20px;
            border-bottom: 1px solid #f3f2ef;
            cursor: pointer;
            transition: background-color 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item:hover {
            background-color: #f3f2ef;
            text-decoration: none;
            color: inherit;
        }

        .conversation-item.active {
            background-color: #e7f3ff;
            border-right: 2px solid #0a66c2;
        }

        .conversation-avatar {
            position: relative;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .conversation-avatar img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .online-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #57c93a;
            border: 2px solid #fff;
            border-radius: 50%;
        }

        .conversation-content {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .contact-name {
            font-size: 14px;
            font-weight: 600;
            color: #000;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .last-message-time {
            font-size: 12px;
            color: #666;
            flex-shrink: 0;
        }

        .conversation-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .last-message {
            font-size: 13px;
            color: #666;
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
        }

        .message-indicators {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .unread-count {
            background: #0a66c2;
            color: #fff;
            font-size: 11px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 16px;
        }

        .empty-state h3 {
            font-size: 18px;
            font-weight: 600;
            color: #000;
            margin: 0 0 8px 0;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.4;
            margin: 0 0 20px 0;
        }

        .btn-primary {
            background: #0a66c2;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .btn-primary:hover {
            background: #004182;
        }

        /* Chat Main Area */
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
        }

        /* Chat Header */
        .chat-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fff;
        }

        .chat-header-info {
            display: flex;
            align-items: center;
        }

        .contact-avatar {
            position: relative;
            margin-right: 12px;
        }

        .contact-avatar img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .online-status {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            background: #57c93a;
            border: 2px solid #fff;
            border-radius: 50%;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
        }

        .contact-details .contact-name {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            margin: 0;
        }

        .contact-status {
            font-size: 12px;
            color: #57c93a;
            margin: 0;
        }

        .chat-actions {
            display: flex;
            gap: 8px;
        }

        /* Messages Container */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            background: #f9f9f9;
        }

        .messages-list {
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        /* Message Wrapper */
        .message-wrapper {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .message-wrapper.sent {
            flex-direction: row-reverse;
        }

        .message-avatar {
            flex-shrink: 0;
        }

        .message-avatar img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .message-content {
            max-width: 70%;
            display: flex;
            flex-direction: column;
        }

        .sent .message-content {
            align-items: flex-end;
        }

        /* Message Bubble */
        .message-bubble {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 16px;
            padding: 12px 16px;
            position: relative;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .sent .message-bubble {
            background: #0a66c2;
            color: #fff;
            border-color: #0a66c2;
        }

        .message-bubble p {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        /* Message Attachment */
        .message-attachment {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0,0,0,0.05);
            border-radius: 8px;
            padding: 8px;
            margin-top: 8px;
        }

        .sent .message-attachment {
            background: rgba(255,255,255,0.2);
        }

        .attachment-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f3f2ef;
            border-radius: 4px;
            color: #666;
        }

        .attachment-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-size: 13px;
            font-weight: 500;
            color: inherit;
        }

        .attachment-size {
            font-size: 11px;
            opacity: 0.7;
        }

        .attachment-download {
            color: inherit;
            text-decoration: none;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .attachment-download:hover {
            background: rgba(0,0,0,0.1);
        }

        /* Message Meta */
        .message-meta {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 4px;
            font-size: 11px;
            color: #666;
        }

        .sent .message-meta {
            justify-content: flex-end;
        }

        .message-status {
            color: #0a66c2;
        }

        /* Message Input Area */
        .message-input-area {
            border-top: 1px solid #e0e0e0;
            background: #fff;
            padding: 16px 20px;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .input-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .input-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
        }

        .input-btn:hover {
            background: #f3f2ef;
            color: #0a66c2;
        }

        .message-input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 40px;
            max-height: 120px;
            padding: 10px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            font-family: inherit;
            resize: none;
            outline: none;
            transition: all 0.2s ease;
        }

        .message-input:focus {
            border-color: #0a66c2;
            box-shadow: 0 0 0 1px #0a66c2;
        }

        .send-button {
            width: 40px;
            height: 40px;
            background: #0a66c2;
            color: #fff;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .send-button:hover {
            background: #004182;
            transform: scale(1.05);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* Input Footer */
        .input-footer {
            margin-top: 8px;
            text-align: right;
        }

        .char-counter {
            color: #666;
            font-size: 11px;
        }

        /* Attachment Preview */
        .attachment-preview {
            background: #f3f2ef;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 8px 12px;
            margin-bottom: 8px;
        }

        .preview-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .remove-attachment {
            background: #dc3545;
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .remove-attachment:hover {
            background: #c82333;
        }

        /* Emoji Picker */
        .emoji-picker {
            position: absolute;
            bottom: 100%;
            left: 0;
            width: 320px;
            height: 280px;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .emoji-header {
            border-bottom: 1px solid #e0e0e0;
            padding: 12px;
        }

        .emoji-tabs {
            display: flex;
            gap: 4px;
        }

        .emoji-tab {
            width: 32px;
            height: 32px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
        }

        .emoji-tab:hover,
        .emoji-tab.active {
            background: #f3f2ef;
            color: #0a66c2;
        }

        .emoji-content {
            padding: 12px;
            height: 200px;
            overflow-y: auto;
        }

        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 4px;
        }

        .emoji-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 4px;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .emoji-item:hover {
            background: #f3f2ef;
            transform: scale(1.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .conversations-sidebar {
                width: 100%;
                position: absolute;
                z-index: 100;
                height: 100%;
            }

            .chat-main {
                display: none;
            }

            .messaging-layout.show-chat .conversations-sidebar {
                display: none;
            }

            .messaging-layout.show-chat .chat-main {
                display: flex;
            }

            .emoji-picker {
                width: 280px;
                height: 240px;
            }

            .emoji-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }

        /* Scrollbar Styling */
        .conversations-list::-webkit-scrollbar,
        .messages-container::-webkit-scrollbar,
        .emoji-content::-webkit-scrollbar {
            width: 6px;
        }

        .conversations-list::-webkit-scrollbar-track,
        .messages-container::-webkit-scrollbar-track,
        .emoji-content::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .conversations-list::-webkit-scrollbar-thumb,
        .messages-container::-webkit-scrollbar-thumb,
        .emoji-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .conversations-list::-webkit-scrollbar-thumb:hover,
        .messages-container::-webkit-scrollbar-thumb:hover,
        .emoji-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>

    <script type="text/javascript">
        // Variables globales
        let currentConversationId = 0;
        let isEmojiPickerOpen = false;

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            initializeMessaging();
        });

        function initializeMessaging() {
            // Initialiser les événements
            setupEventListeners();

            // Initialiser le compteur de caractères
            updateCharacterCount();

            // Auto-scroll vers le bas des messages
            scrollToBottom();

            // Démarrer l'actualisation automatique
            startAutoRefresh();
        }

        function setupEventListeners() {
            const messageInput = document.getElementById('<%= txtMessage.ClientID %>');
            const sendButton = document.getElementById('<%= btnenvoie.ClientID %>');
            const emojiButton = document.getElementById('btnEmoji');
            const attachmentButton = document.getElementById('btnAttachment');
            const fileInput = document.getElementById('fileAttachment');
            const removeAttachmentBtn = document.getElementById('btnRemoveAttachment');

            // Événements de saisie
            if (messageInput) {
                messageInput.addEventListener('input', function() {
                    updateCharacterCount();
                    autoResizeTextarea(this);
                });

                messageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            // Bouton d'envoi
            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }

            // Bouton émoji
            if (emojiButton) {
                emojiButton.addEventListener('click', toggleEmojiPicker);
            }

            // Bouton pièce jointe
            if (attachmentButton) {
                attachmentButton.addEventListener('click', function() {
                    fileInput.click();
                });
            }

            // Sélection de fichier
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelection);
            }

            // Suppression de pièce jointe
            if (removeAttachmentBtn) {
                removeAttachmentBtn.addEventListener('click', removeAttachment);
            }

            // Fermer le sélecteur d'émojis en cliquant ailleurs
            document.addEventListener('click', function(e) {
                const emojiPicker = document.getElementById('emojiPicker');
                if (emojiPicker && !emojiPicker.contains(e.target) && e.target !== emojiButton) {
                    closeEmojiPicker();
                }
            });

            // Gestion des clics sur les émojis
            setupEmojiClickHandlers();
        }

        function updateCharacterCount() {
            const messageInput = document.getElementById('<%= txtMessage.ClientID %>');
            const charCounter = document.getElementById('charCount');

            if (messageInput && charCounter) {
                const length = messageInput.value.length;
                charCounter.textContent = length + '/1000';

                // Changer la couleur selon la limite
                if (length > 900) {
                    charCounter.style.color = '#dc3545';
                } else if (length > 800) {
                    charCounter.style.color = '#ffc107';
                } else {
                    charCounter.style.color = '#666';
                }
            }
        }

        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        function sendMessage() {
            const messageInput = document.getElementById('<%= txtMessage.ClientID %>');
            const sendButton = document.getElementById('<%= btnenvoie.ClientID %>');

            if (messageInput && messageInput.value.trim() !== '') {
                // Désactiver le bouton d'envoi temporairement
                if (sendButton) {
                    sendButton.disabled = true;
                    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                }

                // Déclencher l'envoi côté serveur
                sendButton.click();

                // Réinitialiser après un délai
                setTimeout(function() {
                    if (sendButton) {
                        sendButton.disabled = false;
                        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
                    }
                    messageInput.value = '';
                    updateCharacterCount();
                    autoResizeTextarea(messageInput);
                }, 1000);
            }
        }

        function toggleEmojiPicker() {
            const emojiPicker = document.getElementById('emojiPicker');
            if (emojiPicker) {
                if (isEmojiPickerOpen) {
                    closeEmojiPicker();
                } else {
                    openEmojiPicker();
                }
            }
        }

        function openEmojiPicker() {
            const emojiPicker = document.getElementById('emojiPicker');
            if (emojiPicker) {
                emojiPicker.style.display = 'block';
                isEmojiPickerOpen = true;
            }
        }

        function closeEmojiPicker() {
            const emojiPicker = document.getElementById('emojiPicker');
            if (emojiPicker) {
                emojiPicker.style.display = 'none';
                isEmojiPickerOpen = false;
            }
        }

        function setupEmojiClickHandlers() {
            const emojiItems = document.querySelectorAll('.emoji-item');
            emojiItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    insertEmoji(this.dataset.emoji);
                });
            });
        }

        function insertEmoji(emoji) {
            const messageInput = document.getElementById('<%= txtMessage.ClientID %>');
            if (messageInput) {
                const cursorPos = messageInput.selectionStart;
                const textBefore = messageInput.value.substring(0, cursorPos);
                const textAfter = messageInput.value.substring(cursorPos);

                messageInput.value = textBefore + emoji + textAfter;
                messageInput.focus();
                messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

                updateCharacterCount();
                autoResizeTextarea(messageInput);
                closeEmojiPicker();
            }
        }

        function handleFileSelection() {
            const fileInput = document.getElementById('fileAttachment');
            const file = fileInput.files[0];

            if (file) {
                // Validation de la taille (10MB max)
                if (file.size > 10 * 1024 * 1024) {
                    showNotification('Le fichier est trop volumineux (maximum 10 Mo)', 'error');
                    fileInput.value = '';
                    return;
                }

                // Validation de l'extension
                const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar', '.mp3', '.mp4', '.avi'];
                const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

                if (allowedExtensions.indexOf(fileExtension) === -1) {
                    showNotification('Type de fichier non autorisé', 'error');
                    fileInput.value = '';
                    return;
                }

                // Afficher la prévisualisation
                showAttachmentPreview(file);

                // Marquer le fichier comme prêt pour l'upload
                const hdnAttachmentPath = document.getElementById('<%= hdnAttachmentPath.ClientID %>');
                if (hdnAttachmentPath) {
                    hdnAttachmentPath.value = 'READY_TO_UPLOAD';
                }
            }
        }

        function showAttachmentPreview(file) {
            const preview = document.getElementById('attachmentPreview');
            const nameSpan = document.getElementById('attachmentName');

            if (preview && nameSpan) {
                nameSpan.textContent = file.name + ' (' + formatFileSize(file.size) + ')';
                preview.style.display = 'block';
            }
        }

        function removeAttachment() {
            const fileInput = document.getElementById('fileAttachment');
            const preview = document.getElementById('attachmentPreview');
            const hdnAttachmentPath = document.getElementById('<%= hdnAttachmentPath.ClientID %>');

            if (fileInput) fileInput.value = '';
            if (preview) preview.style.display = 'none';
            if (hdnAttachmentPath) hdnAttachmentPath.value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        function scrollToBottom() {
            const messagesContainer = document.querySelector('.messages-container');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        function startAutoRefresh() {
            // Actualiser les messages toutes les 30 secondes
            setInterval(function() {
                const lblId = document.getElementById('<%= lblId.ClientID %>');
                if (lblId && lblId.textContent !== '0') {
                    // Déclencher un postback pour actualiser les messages
                    __doPostBack('<%= Page.ClientID %>', 'RefreshMessages');
                }
            }, 30000);
        }

        function showNotification(message, type = 'info') {
            // Créer une notification toast
            const notification = document.createElement('div');
            notification.className = 'notification toast-' + type;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#dc3545' : '#0a66c2'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(notification);

            // Supprimer après 3 secondes
            setTimeout(function() {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Animations CSS pour les notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Gestion responsive
        function handleResponsive() {
            const isMobile = window.innerWidth <= 768;
            const messagingLayout = document.querySelector('.messaging-layout');

            if (isMobile && currentConversationId > 0) {
                messagingLayout.classList.add('show-chat');
            } else {
                messagingLayout.classList.remove('show-chat');
            }
        }

        // Écouter les changements de taille d'écran
        window.addEventListener('resize', handleResponsive);

        // Marquer les conversations comme actives
        function setActiveConversation(conversationId) {
            currentConversationId = conversationId;

            // Retirer la classe active de toutes les conversations
            const conversationItems = document.querySelectorAll('.conversation-item');
            conversationItems.forEach(item => item.classList.remove('active'));

            // Ajouter la classe active à la conversation sélectionnée
            const activeItem = document.querySelector(`[data-conversation-id="${conversationId}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }

            handleResponsive();
        }
    </script>

</asp:Content>
